import { roleApi } from '@gt/mis-components-web/apis'
//start table配置
export const searchItems = [
  {
    operator: 'like',
    renderer: {
      type: 'TextField',
      options: {
        field: 'username',
        label: '用户名'
      }
    }
  },
  {
    operator: 'like',
    renderer: {
      type: 'TextField',
      options: {
        field: 'nickname',
        label: '姓名'
      }
    }
  }
  // {
  //   operator: 'like',
  //   renderer: {
  //     type: 'TextField',
  //     options: {
  //       field: 'phone',
  //       label: '手机'
  //     }
  //   }
  // }
]
export const recordOperators: TABLE.RECORD_OPERATOR[] = [
  {
    key: '_edit',
    label: '编辑'
    // color: '#f00',
    // expression: 'state=='1''
  },
  {
    key: '_reset',
    label: '重置',
    confirm: true
  },
  {
    key: '_delete',
    label: '删除',
    color: '#f00',
    confirm: true,
    showRoles: ['admin']
  },
  {
    key: '_stop',
    label: '停用',
    color: '#f00',
    confirm: true,
    expression: (data) => {
      return data.enabled
    }
  },
  {
    key: '_start',
    label: '启用',
    color: 'green',
    confirm: true,
    expression: (data) => {
      return !data.enabled
    }
  }
]

export const tableOperators: TABLE.TABLE_OPERATORS[] = [
  { key: '_add', label: '添加', icon: 'UserAddOutlined' }
]

export const tableFields: TABLE.FIELDS[] = [
  {
    name: 'username',
    label: '用户名'
  },
  {
    name: 'nickname',
    label: '姓名'
  },
  {
    name: 'phone',
    label: '手机号',
    align: 'center'
  },
  {
    name: 'role_name',
    type: 'json',
    label: '角色',
    align: 'center'
  },
  {
    name: 'cname',
    type: 'text',
    label: '市名称'
  },
  {
    name: 'fname',
    type: 'text',
    label: '县名称'
  },
  {
    name: 'tname',
    type: 'text',
    label: '乡镇名称'
  },
  {
    name: 'enabled',
    align: 'center',
    label: '状态',
    dict: '状态'
  }
]
export const dicts: FORM.DICT[] = [
  {
    name: 'yn',
    type: 'inline',
    items: [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]
  },
  {
    name: '状态',
    type: 'inline',
    items: [
      {
        label: '已停用',
        value: false,
        tagColor: 'error',
        tag: true
      },
      {
        label: '运行中',
        value: true,
        tagColor: 'success',
        tag: true
      }
    ]
  }
]
//end table配置

//start GTForm配置

import { cloneDeep } from 'lodash'

export const formDicts: FORM.DICT[] = [
  {
    name: 'hideshow',
    type: 'inline',
    items: [
      {
        label: '显示',
        value: 1
      },
      {
        label: '隐藏',
        value: 0
      }
    ]
  },
  {
    name: 'yn',
    type: 'inline',
    items: [
      {
        label: '是',
        value: 1
      },
      {
        label: '否',
        value: 0
      }
    ]
  },
  {
    type: 'table',
    name: 'admin_city',
    labelField: 'cname',
    valueField: 'ccode',
    fields: ['cname', 'ccode'],
    orderField: 'ccode'
  },
  {
    type: 'table',
    name: 'admin_county',
    labelField: 'fname',
    valueField: 'fcode',
    fields: ['fname', 'fcode', 'ccode'],
    orderField: 'fcode'
  },
  {
    type: 'table',
    name: 'admin_town',
    labelField: 'tname',
    valueField: 'tcode',
    fields: ['tcode', 'tname', 'fcode'],
    orderField: 'tcode'
  }
]

const layout = []
export const adminLayout = [
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'username',
      label: '用户名',
      required: true
    }
  },
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'password',
      label: '密码',
      mode: 'password',
      required: true
    }
  }
]
export const baseLayout = [
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'nickname',
      label: '姓名',
      required: true
    }
  },
  {
    type: 'SelectField',
    options: {
      type: 'text',
      // mode: 'multiple',
      field: 'role',
      dictRef: 'role',
      required: true,
      label: '角色'
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'role_code',
      label: '角色code',
      readonly: true,
      required: true,
      calculator: ['get', 'code', 'role']
    }
  },
  {
    type: 'SelectField',
    options: {
      type: 'text',
      field: 'ccode',
      dictRef: 'admin_city',
      depends: ['==', 'role_code', 'growers'],
      required: true,
      label: '市名称'
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'cname',
      label: '市名称',
      readonly: true,
      required: true,
      depends: ['==', 'role_code', 'growers'],
      calculator: ['get', 'cname', 'ccode']
    }
  },
  {
    type: 'SelectField',
    options: {
      type: 'text',
      field: 'fcode',
      dictRef: 'admin_county',
      depends: ['==', 'role_code', 'growers'],
      filter: ['=', 'ccode', 'ccode'],
      required: true,
      label: '县名称'
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'fname',
      label: '县名称',
      readonly: true,
      required: true,
      depends: ['==', 'role_code', 'growers'],
      calculator: ['get', 'fname', 'fcode']
    }
  },
  {
    type: 'SelectField',
    options: {
      type: 'text',
      field: 'tcode',
      dictRef: 'admin_town',
      filter: ['=', 'fcode', 'fcode'],
      lazy: true,
      required: true,
      depends: ['==', 'role_code', 'growers'],
      label: '乡镇名称'
    }
  },
  {
    type: 'TextField',
    options: {
      hide: true,
      type: 'text',
      field: 'tname',
      label: '乡镇名称',
      readonly: true,
      required: true,
      depends: ['==', 'role_code', 'growers'],
      calculator: ['get', 'tname', 'tcode']
    }
  },
  {
    type: 'TextField',
    options: {
      type: 'text',
      field: 'phone',
      maxLength: 11,
      label: '手机号'
    }
  }
  // {
  //   type: 'TextField',
  //   options: {
  //     type: 'text',
  //     field: 'email',
  //     label: '邮箱'
  //   }
  // },
  // {
  //   type: 'TextField',
  //   options: {
  //     type: 'text',
  //     field: 'company',
  //     label: '单位'
  //   }
  // },
  // {
  //   type: 'TextField',
  //   options: {
  //     type: 'text',
  //     field: 'department',
  //     label: '科室'
  //   }
  // }
]

const validators = {
  fieldValidators: [
    {
      field: 'username',
      label: '用户名',
      validators: [
        {
          type: 'regex',
          options: {
            pattern: /^\S+$/,
            message: '用户名不能包含空格'
          }
        },
        {
          type: 'regex',
          options: {
            pattern: /^[a-zA-Z][a-zA-Z0-9]{3,15}$/,
            message: '必须由英文开头,仅可包含英文和数字,4-16位'
          }
        }
      ]
    },
    // {
    //   field: 'password',
    //   label: '密码',
    //   validators: [
    //     {
    //       type: 'regex',
    //       options: {
    //         // pattern: /^[a-zA-Z0-9]{6,16}$/,
    //         // message: '仅可包含英文和数字,6-16位'
    //         pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&+=.])(?=.*[^\w\d\s])\S{8,}$/,
    //         message: '必须包含大写字母、小写字母、数字和特殊字符，不少于8位'
    //       }
    //     }
    //   ]
    // },
    {
      field: 'phone',
      label: '手机号',
      validators: [
        {
          type: 'regex',
          options: {
            pattern:
              /^1(3[0-9]|4[5,7]|5[0,1,2,3,5,6,7,8,9]|6[2,5,6,7]|7[0,1,2,3,5,6,7,8]|8[0-9]|9[1,5,8,9])\d{8}$/,
            message: '请输入正确的手机号'
          }
        }
      ]
    },
    {
      field: 'email',
      label: '邮箱',
      validators: [
        {
          type: 'regex',
          options: {
            pattern:
              /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/,
            message: '请输入正确的邮箱'
          }
        }
      ]
    }
  ]
}
const formDef = {
  layout,
  formDicts,
  validators
}
// interface FormDef {
//   layout?: object
//   dicts?: object[]
//   dictRefs?: object
//   validators: object
// }
export interface FormRecordData {
  formDef: FORM.DEF
  formRecordData: any | undefined
}
export const formOptions: FormRecordData = {
  formDef,
  formRecordData: {}
}
//组合layout
export function resultLayout(type) {
  const _adminLayout = cloneDeep(adminLayout)
  const _baseLayout = cloneDeep(baseLayout)
  let arr: any[] = []
  switch (type) {
    case '添加':
      arr = [..._adminLayout, ..._baseLayout]
      break
    case '编辑':
      arr = [_adminLayout[0], ..._baseLayout]
  }
  return arr
}
//获取角色
export async function getRoles() {
  let arr: any[] = []
  try {
    const { list } = await roleApi.queryAllRole()
    arr = list.map((item) => ({ label: item.name, code: item.code, value: item._id }))
  } catch (error) {
    console.error(error)
  }
  return arr
}

//end GTForm配置
