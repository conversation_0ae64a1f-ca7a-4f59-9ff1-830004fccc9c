<template>
  <div>
    <div class="draw-widget">
      <template v-if="drawing">
        <div class="draw-item" @click="backClick()">
          <a-icon type="close" />
        </div>
        <div class="draw-item" @click="redoPoint()">
          <img class="icon" :src="icon1" />
        </div>
        <div class="draw-item" @click="saveHandler">
          <img class="icon" :src="icon2" />
        </div>
      </template>
      <div v-else class="draw-item" @click="addPoint()">
        <img class="icon" :src="icon3" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import icon1 from '@/assets/images/map/draw-return.png'
import icon2 from '@/assets/images/map/draw-check.png'
import icon3 from '@/assets/images/map/polygon-select.png'

// Emits 定义
interface Emits {
  /** 绘制状态改变事件 */
  (e: 'change', drawing: boolean): void
  /** 撤销操作事件 */
  (e: 'redo'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
/** 是否正在绘制 */
const drawing = ref<boolean>(false)

// 方法定义
/** 开始绘制点 */
const addPoint = (): void => {
  drawing.value = true
  emit('change', true)
}

/** 撤销点操作 */
const redoPoint = (): void => {
  emit('redo')
}

/** 取消绘制 */
const backClick = (): void => {
  drawing.value = false
  emit('change', false)
}

/** 保存绘制结果 */
const saveHandler = (): void => {
  drawing.value = false
  emit('change', false)
}
</script>

<style lang="less" scoped>
.draw-widget {
  position: absolute;
  top: 130px;
  right: 30px;
  display: flex;
  flex-direction: column;
  z-index: 8;

  .draw-item {
    width: 30px;
    height: 30px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    backdrop-filter: blur(4px);
    cursor: pointer;

    .icon {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
