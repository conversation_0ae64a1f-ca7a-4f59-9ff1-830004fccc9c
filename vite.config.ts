import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import AutoImport from 'unplugin-auto-import/vite'
// import Components from 'unplugin-vue-components/vite'
// import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  // console.log(' command, mode  :>> ', command, mode)
  const { VITE_APP_URL, VITE_APP_AXIOS_BASE_URL } = loadEnv(mode, process.cwd(), '')
  // console.log('VITE_APP_AXIOS_BASE_URL :>> ', VITE_APP_URL)
  return {
    plugins: [
      vue(),
      vueJsx(),
      AutoImport({
        eslintrc: {
          enabled: false, // true用于生成eslint配置,生成后改回false，避免重复生成消耗
          filepath: './.eslintrc-auto-import.json',
          globalsPropValue: true
        },
        imports: ['vue', 'vue-router'],
        dts: 'src/types/auto-import.d.ts',
        // 是否在vue模板中自动导入
        vueTemplate: true
      })
      // Components({
      //   // 组件的有效文件扩展名。
      //   extensions: ['vue'],
      //   // 搜索子目录
      //   deep: true,
      //   // 自定义组件的解析器
      //   // resolvers: [],
      //   resolvers: [AntDesignVueResolver({ importStyle: 'less', resolveIcons: true })],
      //   // 生成 `components.d.ts` 全局声明，
      //   // 也接受自定义文件名的路径
      //   // dts: true,
      //   // 允许子目录作为组件的命名空间前缀。
      //   directoryAsNamespace: false,
      //   // 忽略命名空间前缀的子目录路径
      //   globalNamespaces: [],
      //   // 自动导入指令
      //   // 默认值：Vue 3 的`true`，Vue 2 的`false`
      //   // 需要 Babel 来为 Vue 2 进行转换，出于性能考虑，它默认处于禁用状态。
      //   directives: true,
      //   // filters for transforming targets
      //   include: [/.vue$/, /.vue?vue/],
      //   dts: './src/types/components.d.ts'
      // })
    ],
    // css: {
    //   preprocessorOptions: {
    //     less: {
    //       javascriptEnabled: true,
    //       modifyVars: {
    //         // 在这里自定义主题色等样式
    //         'primary-color': '#1677ff'
    //         // 'link-color': '#1da57a',
    //         // 'border-radius-base': '2px'
    //       }
    //     }
    //   }
    // },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    base: VITE_APP_URL,
    build: {
      // target: ['modules'], // 设置最终构建的浏览器兼容目标
      polyfillModulePreload: true, // 是否自动注入 module preload 的 polyfill
      emptyOutDir: true, // 构建时清空该目录
      chunkSizeWarningLimit: 1500, // chunk 大小警告的限制
      // 指定输出路径(相对与根目录)
      outDir: 'dist',
      // 指定生成静态资源的存放路径(相对outdir)
      // assetsDir: 'static',
      // cssCodeSplit: true,
      // sourcemap: false, // 构建后是否生成 source map 文件
      rollupOptions: {
        input: 'index.html',
        output: {
          // 静态资源打包做处理
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks(id) {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString()
            }
          }
        }
      }
    },
    server: {
      port: 8080,
      host: '0.0.0.0',
      // open: false //服务器运行后自动打开网页
      proxy: {
        // VITE_APP_AXIOS_BASE_URL
        // 代理配置
        [VITE_APP_AXIOS_BASE_URL]: `http://************/`
      }
    }
  }
})
