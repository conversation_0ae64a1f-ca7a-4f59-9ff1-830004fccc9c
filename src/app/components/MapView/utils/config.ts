export const geomTypes = {
  point: [
    {
      id: '__draw_layer_point_circle_id__',
      type: 'circle',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: []
          }
        }
      },
      paint: {
        'circle-color': '#fff',
        'circle-radius': 4,
        'circle-stroke-width': 4,
        'circle-stroke-color': '#FE0000'
      }
    }
  ],
  line: [
    {
      id: '__draw_layer_line_point_id__',
      type: 'circle',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'MultiPoint',
            coordinates: []
          }
        }
      },
      paint: {
        'circle-color': '#fff',
        'circle-radius': 4,
        'circle-stroke-width': 3,
        'circle-stroke-color': '#FE0000'
      }
    },
    {
      id: '__draw_layer_line_id__',
      type: 'line',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: []
          }
        }
      },
      paint: {
        'line-width': 3,
        'line-color': '#FC8046'
      }
    },
    {
      id: '__draw_layer_line_assist_id__',
      type: 'line',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: []
          }
        }
      },
      paint: {
        'line-width': 3,
        'line-color': '#FE0000',
        'line-dasharray': [1, 1]
      }
    }
  ],
  polygon: [
    {
      id: '__draw_layer_polygon_fill_id__',
      type: 'fill',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: []
          }
        }
      },
      paint: {
        'fill-color': '#FC8046',
        'fill-opacity': 0.5
      }
    },
    {
      id: '__draw_layer_polygon_line_id__',
      type: 'line',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: []
          }
        }
      },
      paint: {
        'line-width': 3,
        'line-color': '#FC8046'
      }
    },
    {
      id: '__draw_layer_polygon_point_id__',
      type: 'circle',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'MultiPoint',
            coordinates: []
          }
        }
      },
      paint: {
        'circle-color': '#fff',
        'circle-radius': 4,
        'circle-stroke-width': 3,
        'circle-stroke-color': '#FE0000'
      }
    }
  ],
  circle: [
    {
      id: '__draw_layer_circle_fill_id__',
      type: 'fill',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: []
          }
        }
      },
      paint: {
        'fill-color': '#FC8046',
        'fill-opacity': 0.5
      }
    },
    {
      id: '__draw_layer_circle_line_id__',
      type: 'line',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: []
          }
        }
      },
      paint: {
        'line-width': 3,
        'line-color': '#FC8046'
      }
    },
    {
      id: '__draw_layer_circle_r_id__',
      type: 'line',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: []
          }
        }
      },
      paint: {
        'line-width': 3,
        'line-color': '#FE0000',
        'line-dasharray': [1, 1]
      }
    },

    {
      id: '__draw_layer_circle_point_id__',
      type: 'circle',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'MultiPoint',
            coordinates: []
          }
        }
      },
      paint: {
        'circle-color': '#fff',
        'circle-radius': 4,
        'circle-stroke-width': 3,
        'circle-stroke-color': '#FE0000'
      }
    }
  ]
}
export const getPointSourceData = (coord: any[]) => {
  if (coord.length === 1) {
    return {
      type: 'Feature',
      geometry: {
        type: 'Point',
        coordinates: coord[0]
      },
      properties: {}
    }
  } else {
    return {
      type: 'Feature',
      geometry: {
        type: 'MultiPoint',
        coordinates: coord
      },
      properties: {}
    }
  }
}

export const getFeatureData = (coord: any, type: string) => {
  return {
    type: 'Feature' as const,
    geometry: {
      type: type as any,
      coordinates: coord
    },
    properties: {}
  }
}
