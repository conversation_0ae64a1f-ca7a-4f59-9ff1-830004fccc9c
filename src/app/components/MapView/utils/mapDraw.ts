import { circle } from './helper'
import { geomTypes, getPointSourceData, getFeatureData } from './config'
import * as mapboxgl from 'mapbox-gl'
import * as turf from '@turf/turf'

// 坐标类型定义
type Coordinate = [number, number]
type CoordinateArray = Coordinate[]

// 绘制类型定义
type DrawType = 'point' | 'line' | 'polygon' | 'circle'

// 消息接口定义
interface MessageItem {
  label: string
  val: string
}

// 上下文接口定义
interface DrawContext {
  showMessage: (messages: MessageItem[] | null) => void
}

// 图层配置接口定义（使用 mapboxgl 的 AnyLayer 类型）
type LayerConfig = mapboxgl.AnyLayer

// 源数据函数类型定义
type SourceFunction = (coord: CoordinateArray) => any

const getSourceFunctions: Record<string, SourceFunction> = {
  __draw_layer_point_circle_id__: getPointSourceData,
  __draw_layer_line_point_id__: getPointSourceData,
  __draw_layer_line_id__: (coord: CoordinateArray) => {
    return getFeatureData(coord, 'LineString')
  },
  __draw_layer_polygon_point_id__: getPointSourceData,
  __draw_layer_polygon_line_id__: (coord: CoordinateArray) => {
    return getFeatureData([...coord, coord[0]], 'LineString')
  },
  __draw_layer_polygon_fill_id__: (coord: CoordinateArray) => {
    if (coord.length < 3) {
      return getFeatureData([], 'Polygon')
    }
    return getFeatureData([[...coord, coord[0]]], 'Polygon')
  },
  __draw_layer_circle_point_id__: getPointSourceData,
  __draw_layer_circle_line_id__: (coord: CoordinateArray) => {
    if (coord.length !== 2) {
      return getFeatureData([], 'LineString')
    }
    const res = circle(coord[0], coord[1])
    return getFeatureData(res, 'LineString')
  },
  __draw_layer_circle_fill_id__: (coord: CoordinateArray) => {
    if (coord.length !== 2) {
      return getFeatureData([], 'Polygon')
    }
    const res = circle(coord[0], coord[1])
    return getFeatureData([res], 'Polygon')
  }
}

export default class DrawTool {
  /** 地图实例 */
  map: mapboxgl.Map | null = null
  /** 当前绘制类型 */
  drawType: DrawType | null = null
  /** 点击处理器 */
  clickHandlers: any = null
  /** 坐标数组 */
  coordinates: CoordinateArray = []
  /** 图层ID数组 */
  layers: string[] = []
  /** 弹窗数组 */
  popups: mapboxgl.Popup[] = []
  /** 上下文对象 */
  context: DrawContext
  /** 辅助弹窗 */
  assistPopup: mapboxgl.Popup = new mapboxgl.Popup({
    className: 'draw-info-popup',
    closeOnClick: false,
    closeButton: false
  })

  constructor(map: mapboxgl.Map, context: DrawContext) {
    this.context = context
    this.map = map
  }

  /**
   * 激活绘制工具
   * @param type 绘制类型
   */
  activate(type: DrawType): void {
    this.coordinates = []
    this.drawType = type
    this.addDrawLayer()
    if (type === 'polygon') {
      this.popups.push(
        new mapboxgl.Popup({
          className: 'draw-info-popup',
          closeOnClick: false,
          closeButton: false
          // anchor: "top-left",
        })
      )
    }
  }

  /**
   * 停用绘制工具
   */
  deactivate(): void {
    this.coordinates = []
    this.drawType = null
    if (this.popups.length) {
      this.popups.forEach((p) => {
        p.remove()
      })
    }
    if (this.assistPopup) {
      this.assistPopup.remove()
    }
    this.popups = []
    this.addDrawLayer()
    this.context.showMessage(null)
  }

  /**
   * 添加当前类型所需图层
   */
  addDrawLayer(): void {
    const arr: string[] = []
    Object.keys(geomTypes).forEach((type) => {
      if (type === this.drawType) {
        const layers = geomTypes[type as keyof typeof geomTypes] as LayerConfig[]
        layers.forEach((item: LayerConfig) => {
          if (this.map && !this.map.getLayer(item.id)) {
            console.log(item)
            this.map.addLayer(item)
            console.log(this.map.getLayer(item.id))
          }
          arr.push(item.id)
        })
      } else {
        const layers = geomTypes[type as keyof typeof geomTypes] as LayerConfig[]
        layers.forEach((item: LayerConfig) => {
          if (this.map && this.map.getLayer(item.id)) {
            this.map.removeLayer(item.id)
            this.map.removeSource(item.id)
          }
        })
      }
    })
    this.layers = arr
    console.log(this.layers)
  }

  /**
   * 添加坐标点
   * @param coord 点位坐标
   */
  addCoordinates(coord: Coordinate): void {
    if (this.drawType === 'circle' && this.coordinates.length === 2) {
      this.coordinates.pop()
      if (this.map) {
        const lonLat = this.map.getCenter()
        this.setMapCenter([lonLat.lng, lonLat.lat])
      }
    }
    if (this.drawType === 'polygon' && this.coordinates.length >= 3) {
      const flag = this.checkCrosses(coord)
      if (flag) return
    }
    this.coordinates.push(coord)
    if (this.drawType === 'line' && this.coordinates.length >= 2) {
      this.addLinePopup()
      this.assistPopup.remove()
      if (this.map) {
        const source = this.map.getSource('__draw_layer_line_assist_id__') as mapboxgl.GeoJSONSource
        if (source) {
          source.setData({
            type: 'Feature' as const,
            geometry: {
              type: 'LineString' as const,
              coordinates: []
            },
            properties: {}
          })
        }
      }
    }
    if (this.drawType === 'polygon' && this.coordinates.length >= 2) {
      this.addPolygonPopup()
    }

    this._updateDrawFeature()
    console.log(this.popups)
  }

  /**
   * 判断多边形是否自相交
   * @param coord 新添加点位坐标
   */
  checkCrosses(coord: Coordinate): boolean {
    let flag = false
    const lastCoord = this.coordinates[this.coordinates.length - 1]
    if (!lastCoord) return false

    const lastLine = turf.lineString([coord, lastCoord])
    for (let i = 0; i < this.coordinates.length - 2; i++) {
      const currentCoord = this.coordinates[i]
      const nextCoord = this.coordinates[i + 1]
      if (!currentCoord || !nextCoord) continue

      const line = turf.lineString([currentCoord, nextCoord])
      const cross = turf.lineIntersect(lastLine, line)

      if (cross.features.length) flag = true
    }
    if (flag) {
      // 显示错误消息，但这里需要传递正确的消息格式
      this.context.showMessage([
        {
          label: '错误',
          val: '绘制范围存在自相交，请仔细核对'
        }
      ])
    }
    return flag
  }

  /**
   * 撤回坐标点
   */
  redoCoordinates(): void {
    if (!this.coordinates.length) return

    if (this.drawType === 'line') {
      if (this.coordinates.length >= 2) {
        console.log(this.popups)
        const lastPopup = this.popups[this.popups.length - 1]
        if (lastPopup) {
          lastPopup.remove()
          this.popups.pop()
        }
      }

      this.clearAssist('__draw_layer_line_assist_id__', 'LineString')
    }

    this.coordinates.pop()

    if (this.drawType === 'circle') {
      if (this.coordinates.length === 0) {
        this.clearAssist('__draw_layer_circle_r_id__', 'LineString')
      }
      if (this.coordinates.length === 1 && this.map) {
        const lonLat = this.map.getCenter()
        this.setMapCenter([lonLat.lng, lonLat.lat])
      }
    }

    if (this.drawType === 'polygon') {
      if (this.coordinates.length <= 2) {
        const firstPopup = this.popups[0]
        if (firstPopup) {
          firstPopup.remove()
        }
      }
      if (this.coordinates.length >= 1) {
        const lastPopup = this.popups[this.popups.length - 1]
        if (lastPopup) {
          lastPopup.remove()
          this.popups.pop()
        }
        if (this.coordinates.length >= 2) {
          this.addPolygonPopup(true)
        }
      }
    }

    this._updateDrawFeature()
  }

  /**
   * 更新图层数据
   */
  _updateDrawFeature(): void {
    console.log(this.drawType)
    if (!this.map) return

    switch (this.drawType) {
      case 'point':
        this.layers.forEach((item) => {
          const sourceFunc = getSourceFunctions[item]
          if (sourceFunc) {
            const data = sourceFunc(this.coordinates)
            const source = this.map!.getSource(item) as mapboxgl.GeoJSONSource
            if (source) {
              source.setData(data)
            }
          }
        })
        break
      case 'line':
        this.layers.forEach((item) => {
          const sourceFunc = getSourceFunctions[item]
          if (!sourceFunc) return
          const data = sourceFunc(this.coordinates)
          const source = this.map!.getSource(item) as mapboxgl.GeoJSONSource
          if (source) {
            source.setData(data)
          }
        })
        this.lineInfoEmit()
        break
      case 'polygon':
        this.layers.forEach((item) => {
          const sourceFunc = getSourceFunctions[item]
          if (sourceFunc) {
            const data = sourceFunc(this.coordinates)
            const source = this.map!.getSource(item) as mapboxgl.GeoJSONSource
            if (source) {
              source.setData(data)
            }
          }
        })
        this.polygonInfoEmit()
        break
      case 'circle':
        this.layers.forEach((item) => {
          const sourceFunc = getSourceFunctions[item]
          if (!sourceFunc) return
          const data = sourceFunc(this.coordinates)
          const source = this.map!.getSource(item) as mapboxgl.GeoJSONSource
          if (source) {
            source.setData(data)
          }
        })
        this.circleInfoEmit()
        break

      default:
        break
    }
  }
  /**
   * 线绘制过程中描述弹窗
   */
  addLinePopup(): void {
    const lastCoord = this.coordinates[this.coordinates.length - 1]
    const secondLastCoord = this.coordinates[this.coordinates.length - 2]

    if (!lastCoord || !secondLastCoord) return

    const lastCoordinates: [Coordinate, Coordinate] = [lastCoord, secondLastCoord]
    console.log(lastCoordinates)

    const val = this.getDistance(lastCoordinates)
    const lonLat = this.getMidpoint(lastCoordinates[0], lastCoordinates[1])

    const popup = new mapboxgl.Popup({
      className: 'draw-info-popup',
      closeOnClick: false,
      closeButton: false
      // anchor: "top-left",
    })

    if (this.map) {
      popup
        .setLngLat(lonLat as [number, number])
        .setHTML(
          `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
        )
        .addTo(this.map)
      this.popups.push(popup)
    }
  }
  /**
   * 多边形绘制过程中描述弹窗
   * @param refresh 是否刷新
   */
  addPolygonPopup(refresh?: boolean): void {
    if (!refresh) {
      const lastCoord = this.coordinates[this.coordinates.length - 1]
      const secondLastCoord = this.coordinates[this.coordinates.length - 2]

      if (!lastCoord || !secondLastCoord) return

      const lastCoordinates: [Coordinate, Coordinate] = [lastCoord, secondLastCoord]
      console.log(lastCoordinates)

      const val = this.getDistance(lastCoordinates) as string
      const lonLat = this.getMidpoint(lastCoordinates[0], lastCoordinates[1])

      const popup = new mapboxgl.Popup({
        className: 'draw-info-popup',
        closeOnClick: false,
        closeButton: false
        // anchor: "top-left",
      })

      if (this.map) {
        popup
          .setLngLat(lonLat as [number, number])
          .setHTML(
            `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
          )
          .addTo(this.map)
        this.popups.push(popup)
      }
    }

    const firstCoord = this.coordinates[0]
    const lastCoord = this.coordinates[this.coordinates.length - 1]

    if (!firstCoord || !lastCoord) return

    const startToEnd: [Coordinate, Coordinate] = [firstCoord, lastCoord]
    const val1 = this.getDistance(startToEnd) as string
    const lonLat1 = this.getMidpoint(startToEnd[0], startToEnd[1])

    const firstPopup = this.popups[0]
    if (firstPopup && this.map) {
      firstPopup
        .setLngLat(lonLat1 as [number, number])
        .setHTML(
          `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val1}</text>`
        )
        .addTo(this.map)
    }

    // 这里是统计面积
    // if (this.coordinates.length >= 3) {
    //   const p = turf.polygon([[...this.coordinates, this.coordinates[0]]]);
    //   const lonLat2 = turf.centerOfMass(p).geometry.coordinates;
    //   const val = this.getArea([...this.coordinates, this.coordinates[0]]);

    //   // this.popups[0]
    //   //   .setLngLat(lonLat1)
    //   //   .setHTML(
    //   //     `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val1}</text>`
    //   //   )
    //   //   .addTo(this.map);
    //   this.popups[0]
    //     .setLngLat(lonLat2)
    //     .setHTML(
    //       `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
    //     )
    //     .addTo(this.map);
    // }
  }

  /**
   * 地图视图中心发生变化执行
   * @param e 新的中心点坐标
   */
  setMapCenter(e: Coordinate): void {
    if (!this.map) return

    if (this.drawType === 'circle' && this.coordinates.length === 1) {
      const firstCoord = this.coordinates[0]
      if (!firstCoord) return

      const coord: [Coordinate, Coordinate] = [firstCoord, e]
      const data = getFeatureData(coord, 'LineString')
      const source = this.map.getSource('__draw_layer_circle_r_id__') as mapboxgl.GeoJSONSource
      if (source) {
        source.setData(data)
      }
      const val = this.getDistance(coord) as string
      const midpoint = this.getMidpoint(coord[0], coord[1])
      this.assistPopup
        .setLngLat(midpoint as [number, number])
        .setHTML(
          `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">半径：${val}</text>`
        )
        .addTo(this.map)
    }
    if (this.drawType === 'line' && this.coordinates.length >= 1) {
      const lastCoord = this.coordinates[this.coordinates.length - 1]
      if (!lastCoord) return

      const coord: [Coordinate, Coordinate] = [lastCoord, e]
      const data = getFeatureData(coord, 'LineString')
      const source = this.map.getSource('__draw_layer_line_assist_id__') as mapboxgl.GeoJSONSource
      if (source) {
        source.setData(data)
      }
      const val = this.getDistance(coord) as string
      const midpoint = this.getMidpoint(coord[0], coord[1])
      this.assistPopup
        .setLngLat(midpoint as [number, number])
        .setHTML(
          `<text style="font-size: 12px; line-height: 12px; height: 12px; white-space: nowrap;">${val}</text>`
        )
        .addTo(this.map)
    }
  }

  /**
   * 移除绘制过程中辅助线及弹窗
   * @param layer 图层ID
   * @param type 几何类型
   */
  clearAssist(layer: string, type: string): void {
    this.assistPopup.remove()
    if (this.map) {
      const source = this.map.getSource(layer) as mapboxgl.GeoJSONSource
      if (source) {
        source.setData({
          type: 'Feature' as const,
          geometry: {
            type: type as any,
            coordinates: []
          },
          properties: {}
        })
      }
    }
  }

  /**
   * 计算距离
   * @param coord 坐标数组
   * @param onlyNum 是否只返回数值
   */
  getDistance(coord: CoordinateArray, onlyNum?: boolean): string | number {
    const length = turf.length(turf.lineString(coord))
    if (onlyNum) return length
    let unit = '公里'
    let num = length
    if (length < 1) {
      num = length * 1000
      unit = '米'
    }
    num = parseFloat(num.toFixed(2))
    return `${num}${unit}`
  }

  /**
   * 计算面积
   * @param coord 坐标数组
   */
  getArea(coord: CoordinateArray): string {
    const p = turf.polygon([coord])
    const area = turf.area(p)
    let unit = '平方米'
    let num = area
    if (num > 10000 && num < 1000000) {
      num = area / 10000
      unit = '公顷'
    } else if (num > 1000000) {
      num = area / 1000000
      unit = '平方千米'
    }
    num = parseFloat(num.toFixed(2))
    return `${num}${unit}`
  }

  /**
   * 获取两点中心点
   * @param point1 第一个点
   * @param point2 第二个点
   */
  getMidpoint(point1: Coordinate, point2: Coordinate): Coordinate {
    const result = turf.midpoint(turf.point(point1), turf.point(point2)).geometry.coordinates
    return [result[0], result[1]]
  }

  /**
   * 发送线段信息
   */
  lineInfoEmit(): void {
    if (this.coordinates.length >= 2) {
      const allVal = this.getDistance(this.coordinates) as string
      console.log(allVal)
      this.context.showMessage([
        {
          label: '总长',
          val: allVal
        }
      ])
    } else {
      this.context.showMessage(null)
    }
  }

  /**
   * 发送多边形信息
   */
  polygonInfoEmit(): void {
    if (this.coordinates.length >= 3) {
      const area = this.getArea([...this.coordinates, this.coordinates[0]])
      const distance = this.getDistance([...this.coordinates, this.coordinates[0]]) as string
      this.context.showMessage([
        {
          label: '面积',
          val: area
        },
        {
          label: '周长',
          val: distance
        }
      ])
    } else {
      this.context.showMessage(null)
    }
  }

  /**
   * 发送圆形信息
   */
  circleInfoEmit(): void {
    console.log(this.coordinates)
    if (this.coordinates.length === 2) {
      const radius = this.getDistance(this.coordinates, true) as number
      const area = Math.PI * radius * 1000 * radius * 1000
      console.log(area)
      const circumference = Math.PI * radius * 2
      let result: string
      if (area > 1000000) {
        result = `${parseFloat((area / 1000000).toFixed(2))}平方公里`
      } else if (area > 10000) {
        result = `${parseFloat((area / 10000).toFixed(2))}公顷`
      } else {
        result = `${parseFloat(area.toFixed(2))}平方米`
      }

      this.context.showMessage([
        {
          label: '面积',
          val: result
        },
        {
          label: '周长',
          val:
            circumference > 1
              ? `${parseFloat(circumference.toFixed(2))}公里`
              : `${parseFloat((circumference * 1000).toFixed(2))}米`
        }
      ])
    } else {
      this.context.showMessage(null)
    }
  }

  /**
   * 获取几何数据
   */
  getGeomData(): any {
    console.log(this.drawType, this.coordinates)
    if (!this.drawType || !this.map) return

    switch (this.drawType) {
      case 'point': {
        if (!this.coordinates.length) return
        const pointSource = this.map.getSource(
          '__draw_layer_point_circle_id__'
        ) as mapboxgl.GeoJSONSource
        return pointSource ? (pointSource as any)._data : null
      }

      case 'line': {
        if (this.coordinates.length < 2) return
        const lineSource = this.map.getSource('__draw_layer_line_id__') as mapboxgl.GeoJSONSource
        return lineSource ? (lineSource as any)._data : null
      }

      case 'polygon': {
        if (this.coordinates.length < 3) return
        const polygonSource = this.map.getSource(
          '__draw_layer_polygon_fill_id__'
        ) as mapboxgl.GeoJSONSource
        return polygonSource ? (polygonSource as any)._data : null
      }

      case 'circle': {
        if (this.coordinates.length !== 2) return
        const circleSource = this.map.getSource(
          '__draw_layer_circle_fill_id__'
        ) as mapboxgl.GeoJSONSource
        return circleSource ? (circleSource as any)._data : null
      }
      default:
        break
    }
  }
}
