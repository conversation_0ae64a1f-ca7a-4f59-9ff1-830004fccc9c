<template>
  <div class="box" v-if="layers && layers.length > 0">
    <div v-for="layer in layers" :key="layer.label" class="layer-item">
      <div class="switch">
        <a-switch
          @change="(e) => handleChange(e, layer)"
          v-model="layer.open"
          size="small"
        ></a-switch>
      </div>
      <div class="layer-label">
        <img :src="layer.icon" />
        <span> {{ layer.label }} </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 类型定义
interface LayerItem {
  /** 图层键值 */
  key: string | string[]
  /** 图层是否开启 */
  open: boolean
  /** 图层标识（可选） */
  label?: string
  /** 图层图标（可选） */
  icon?: string
  [key: string]: any
}

// Props 定义
interface Props {
  /** 图层列表 */
  layers?: LayerItem[]
}

defineProps<Props>()

// Emits 定义
interface Emits {
  /** 图层状态改变事件 */
  (e: 'change', layer: LayerItem, checked: boolean): void
}

const emit = defineEmits<Emits>()

// 方法定义
/** 图层开关状态改变处理 */
const handleChange = (checked: any, layer: LayerItem): void => {
  emit('change', layer, checked as boolean)
}
</script>

<style lang="less" scoped>
.box {
  position: absolute;
  right: 20px;
  bottom: 20px;
  z-index: 8;
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
  padding: 20px;
  border-radius: 10px;

  .layer-item {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    &:last-child {
      margin-bottom: 0;
    }
    .switch {
      margin-right: 10px;
    }

    .layer-label {
      display: flex;
      color: #444444;
      align-items: center;

      img {
        width: 20px;
        height: 20px;
        margin-right: 10px;
      }
    }
  }
}
</style>
