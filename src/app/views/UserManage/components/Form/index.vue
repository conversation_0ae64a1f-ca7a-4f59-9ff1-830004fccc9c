<template>
  <GTForm :options="formOptions" ref="formBox"></GTForm>
</template>

<script lang="ts" setup>
defineOptions({ name: 'UserManageForm' })
import { message } from 'ant-design-vue'
import { resultLayout } from './config'
import { userApi } from '@gt/mis-components-web/apis'
import { FormRecordData } from '../../config'
import { parseJson } from '@gt/mis-components-web/utils'
import { cloneDeep } from 'lodash'
const props = defineProps<{
  formData: object
  type: string
  options: FormRecordData
  roleMap: object
}>()
const emits = defineEmits(['refreshTable'])
const { formData, type, options, roleMap } = props
const formDef: any = cloneDeep(options.formDef)
formDef.layout = resultLayout(type)
const formOptions: any = ref({
  formDef: parseJson(formDef as any),
  formRecordData: {} as any
})
if (type !== '添加') {
  const _formData = cloneDeep(formData)
  formOptions.value.formRecordData = _formData
  const { role_code } = _formData as any
  if (role_code) {
    formOptions.value.formRecordData.role = roleMap[role_code[0]].value
    formOptions.value.formRecordData.role_code = role_code[0]
  }
  // let roles = formData['role_code'].map((code: string) => {
  //   if (roleMap[code]) {
  //     return roleMap[code].value
  //   } else {
  //     return ''
  //   }
  // })
  // formOptions.value.formRecordData.roles = roles.filter((item) => item !== '')
  formOptions.value.readonlyFields = 'username'
} else {
  formOptions.value.readonlyFields = ''
}

const formBox = ref()
async function onSubmit() {
  try {
    const { state, data } = await formBox.value.submit()
    const { role, ccode, cname, fcode, fname, tcode, tname } = data as any
    data.roles = [role]
    delete data.role
    delete data.role_code
    delete data.role_name
    data.extraInfo = {
      pname: '山东省',
      pcode: '37',
      ccode,
      cname,
      fcode,
      fname,
      tcode,
      tname
    }
    if (state) {
      if (type === '添加') {
        await userApi.addUser(data)
        message.success('添加成功！')
      } else {
        data._id = data.user_id

        await userApi.updateUser(data)
        message.success('修改成功！')
      }
      emits('refreshTable')
    }
  } catch ({ msg }) {
    console.log(msg)
  }
}
defineExpose({ onSubmit })
</script>

<style scoped></style>
