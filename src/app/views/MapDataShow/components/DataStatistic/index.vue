<template>
  <div class="statistic-content">
    <div class="title">各项目区实施进度</div>
    <div class="statistic-card-list">
      <div
        class="statistic-card"
        v-for="item in progressList"
        :key="item.label"
        :style="{ background: item.color }"
      >
        <div class="text-[16px]">{{ item.label }}</div>
        <div class="flex items-end mt-2">
          <div class="text-[30px] font-bold leading-none">{{ item.value }}</div>
          <div class="text-[16px] ml-2">{{ item.unit }}</div>
        </div>
        <!-- <svgIcon :icon="item.icon"></svgIcon> -->
      </div>
    </div>
    <div class="title">各项目区整地面积排名（亩）</div>
    <div>
      <div v-for="(item, index) in rankList" :key="index">
        <div>项目区1</div>
        <a-progress :percent="50" :size="[300, 20]">
          <template #format>
            <span>{{ item.value }}</span>
          </template>
        </a-progress>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const progressList = ref([
  {
    label: '核心示范点',
    value: 1640.18,
    unit: '亩',
    icon: 'HeatMapOutlined',
    color: 'linear-gradient(to right, #6ACAF2, #529AFD)'
  },
  {
    label: '示范区',
    value: 1536.22,
    unit: '亩',
    icon: '',
    color: 'linear-gradient(to right, #8A96F9, #6289FE)'
  },
  {
    label: '辐射区',
    value: 325.44,
    unit: '亩',
    icon: '',
    color: 'linear-gradient(to right, #F5CCAC, #F47C1D)'
  }
])

const rankList = ref([
  {
    name: '项目区2',
    value: 624.31
  },
  {
    name: '项目区5',
    value: 354.38
  },
  {
    name: '项目区1',
    value: 274.38
  },
  {
    name: '项目区3',
    value: 152.42
  },
  {
    name: '项目区4',
    value: 134.85
  }
])
</script>
<style lang="scss" scoped>
.statistic-content {
  width: 500px;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow: auto;
  .title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-left: 10px;
    position: relative;
    &::after {
      content: '';
      width: 4px;
      height: 70%;
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      margin: auto 0;
      border-radius: 16px;
      background: #1890ff;
    }
  }
}
.statistic-card-list {
  .statistic-card {
    width: 100%;
    height: 110px;
    margin-bottom: 20px;
    border-radius: 10px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 30px;
    color: #fff;
  }
}
</style>
