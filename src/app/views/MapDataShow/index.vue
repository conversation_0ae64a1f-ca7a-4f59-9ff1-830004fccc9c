<template>
  <div class="page-content">
    <div id="map">
      <GTSearch
        class="gtSearch"
        v-if="searchItems"
        :fields="searchItems"
        :defaultFields="defaultFields"
        :showNum="2"
        :listWidth="340"
        @search="searchHandler"
      />
      <legend-widget :layers="layers || []" @change="changelayerData"></legend-widget>
    </div>
    <DataStatistic></DataStatistic>
  </div>
</template>

<script setup lang="ts">
import * as mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import { MAP_BASE_CONFIG, pointOrange, pointBlue, pointGreen, searchItems } from './config'
import DataStatistic from './components/DataStatistic/index.vue'

interface LayerConfig {
  key: string | string[]
  open: boolean
  [key: string]: any
}

interface SelectPoint {
  _id: string
  [key: string]: any
}

interface UserInfo {
  [key: string]: any
}

// 响应式数据
const map = ref<mapboxgl.Map | null>(null)
const layers = ref<LayerConfig[] | null>(null)
const selectPoints = ref<SelectPoint[]>([]) // 选择的点id
const baseZoomLevel = ref<number>(15.5)
const zoomLevel = ref<number | null>(null) // zoom等级
const userInfo = ref<UserInfo | null>(null)
const pointSourceGeoJson = ref<any>(null)
const defaultFields = ref()

// 生命周期
onMounted(async () => {
  userInfo.value = JSON.parse(localStorage.getItem('userInfo') || '{}')
  await initMap()
})

// 方法定义
/** 初始化地图 */
const initMap = (): void => {
  ;(mapboxgl as any).accessToken =
    'pk.eyJ1IjoidmFuamsiLCJhIjoiY2tuemR0MDlrMDI5YzJ2bGNuaThwNjg3ZiJ9.Ix5XwtD2nE4rpqZ6u1j55Q'
  map.value = new mapboxgl.Map(MAP_BASE_CONFIG as unknown as mapboxgl.MapboxOptions)
  map.value.on('load', mapLoaded)
}

/** 地图加载完成回调 */
const mapLoaded = (): void => {
  addImages()
  addPointListener()

  if (!map.value) return

  map.value.on('zoom', () => {
    if (map.value) {
      zoomLevel.value = map.value.getZoom()
    }
  })
  ;(window as any).map = map.value
  // 禁止双指旋转地图
  map.value.touchZoomRotate.disableRotation()
}

/** 添加图片 */
const addImages = (): void => {
  if (!map.value) return

  const imagesList = [
    {
      key: 'green',
      state: 'green',
      iconPath: pointGreen
    },
    {
      key: 'blue',
      state: 'blue',
      iconPath: pointBlue
    },
    {
      key: 'orange',
      state: 'orange',
      iconPath: pointOrange
    }
  ]

  for (const item of imagesList) {
    map.value.loadImage(item.iconPath, (error: any, image: any) => {
      if (error) throw error
      if (map.value) {
        map.value.addImage(item.key, image, {})
      }
    })
  }
}

/** 添加点击监听器 */
const addPointListener = (): void => {
  if (!map.value) return
  const point_list = []
  let lastEventState: any = null

  point_list.forEach(({ point }) => {
    map.value!.on('click', point, async (e: any) => {
      if (lastEventState && Date.now() - lastEventState.timestamp < 200) return
      // 保存这次点击的状态
      lastEventState = {
        timestamp: Date.now(),
        point: e.point
      }
      const features = map.value!.queryRenderedFeatures(e.point)
      await pointClick(features[0].properties)
    })
  })
}

/** 点击点处理 */
const pointClick = async (data: any): Promise<void> => {
  const idToAdd = data._id
  const isFind = selectPoints.value.find((item) => item._id === idToAdd)

  if (isFind) {
    selectPoints.value = selectPoints.value.filter((item) => item._id !== idToAdd)
    await handlePointStyle()
    return
  }

  // 存放选中的数据
  selectPoints.value.push(data)
  await handlePointStyle()
}
/** 过滤图层 */
const changelayerData = (value: LayerConfig): void => {
  if (!map.value || !value) return

  if (Array.isArray(value.key)) {
    value.key.forEach((key) => {
      if (!map.value!.getLayer(key)) return
      map.value!.setLayoutProperty(key, 'visibility', value.open ? 'visible' : 'none')
    })
  } else {
    if (!map.value!.getLayer(value.key)) return
    map.value!.setLayoutProperty(value.key, 'visibility', value.open ? 'visible' : 'none')
  }
}

/** 点位选中的样式处理 */
const handlePointStyle = async (): Promise<void> => {
  if (!map.value) return

  if (selectPoints.value.length > 0) {
    const selectedPoints = selectPoints.value.map((item) => item._id)
    const value = ['match', ['get', '_id'], [...selectedPoints], 0.15, 0.1]
    const point_list = []

    point_list.forEach(({ point }) => {
      map.value!.setLayoutProperty(point, 'icon-size', value)
    })
  } else {
    const value = 0.1
    const point_list = []

    point_list.forEach(({ point }) => {
      map.value!.setLayoutProperty(point, 'icon-size', value)
    })
  }
}
/** 添加点到图层 */
const addPointLayer = (geojson: any, id: string, iconName: string, size: number): void => {
  pointSourceGeoJson.value = geojson

  nextTick(() => {
    if (!map.value) return

    if (map.value.getSource(id)) {
      ;(map.value.getSource(id) as any).setData(geojson)
    } else {
      map.value.addSource(id, {
        type: 'geojson',
        data: geojson
      })
      map.value.addLayer({
        id,
        type: 'symbol',
        source: id,
        minzoom: baseZoomLevel.value,
        layout: {
          'icon-size': size,
          'icon-allow-overlap': true,
          'icon-image': iconName,
          visibility: 'visible'
        }
      })
    }
  })
}

/** 搜索处理器 */
const searchHandler = (values: any): void => {}
</script>
<style scoped lang="less">
.page-content {
  width: 100%;
  height: 100%;
  display: flex;
}
#map {
  position: relative;
  width: 100%;
  height: 100%;
}

.gtSearch {
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 8;
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
  padding: 20px 20px 10px;
  margin: 0;
  // border-radius: 10px;
  width: 100%;
}

.bottom-container {
  background-color: #fff;
  position: absolute;
  padding: 15px;
  width: 400px;
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: 10px;
  z-index: 999;
  border-radius: 10px;
}

.modal {
  /deep/.ant-modal {
    top: 50px;
    overflow: hidden;
    .ant-modal-content {
      max-height: calc(100vh - 100px);
      display: flex;
      flex-direction: column;
      .ant-modal-header,
      .ant-modal-footer {
        flex-shrink: 0;
      }
      .ant-modal-body {
        padding: 20px;
        flex: 1;

        overflow-y: auto;
      }
    }
  }
}
</style>
