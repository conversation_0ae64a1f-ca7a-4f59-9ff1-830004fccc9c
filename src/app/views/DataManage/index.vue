<template>
  <Suspense>
    <GTSearchTable
      v-if="tableOptions"
      ref="GTSearchTableRef"
      :tableOptions="tableOptions"
      @operate="operateHandler"
      :formName="formName"
      :showTitle="false"
      :yOffset="yOffset"
    />
  </Suspense>

  <a-modal
    v-model:open="modalVisible"
    title="查看"
    :maskClosable="false"
    destroyOnClose
    width="60%"
    :zIndex="10"
    :footer="null"
  >
    <GTDetail :options="formOptions" />
  </a-modal>
</template>

<script lang="ts" setup>
defineOptions({ name: 'DataManage' })

import { message } from 'ant-design-vue'

import { GTSearchTable } from '@gt/mis-components-web'
import { formDesignApi, formDataApi } from '@gt/mis-components-web/apis'
import { toFrontFormData, toApiFormData } from '@gt/mis-components-web/utils'

import { searchItems, tableOperators, recordOperators } from './config'

const userInfo = reactive(JSON.parse(localStorage.getItem('userInfo') || '{}'))

const route = useRoute()
const formName = ref(route.path.split('/').at(-1) || 'soil_preparation')
const GTSearchTableRef = ref()

const modalVisible = ref(false)
const formOptions = reactive({
  formDef: {},
  formRecordData: {},
  readonlyFields: '',
  viewConfig: {
    labelAlign: 'left'
  }
})

const yOffset = computed(() => {
  return 75 + 20 + 48 + 40 + 50 + 42 + 52
})

const tableOptions = computed(() => {
  const obj: any = {}
  obj.searchItems = searchItems
  obj.tableOperators = tableOperators
  obj.recordOperators = recordOperators
  obj.modalWidth = 600

  return obj
})

onMounted(() => {
  getFormDef()
})

const getFormDef = async () => {
  try {
    let res = await formDesignApi.queryFormDesign(formName.value)
    formOptions.formDef = res

    console.log('res', res)
  } catch (error) {
    message.error(error || '表单获取失败')
  }
}

const operateHandler = async ({ key, data }) => {
  switch (key) {
    case '_view':
      getRecordData(data)
      break
    case '_download':
      downloadHandler(data)
      break
    default:
      break
  }
}

const getRecordData = async (data) => {
  try {
    const { list = [] } = await formDataApi.searchFormData(formName.value, {
      filter: ['=', '_id', data['_id']],
      resolveSubItems: true
    })
    formOptions.formRecordData = toFrontFormData(list[0] || {})

    modalVisible.value = true
  } catch (error) {
    message.error(error.msg || '获取数据失败！')
  }
}

const downloadHandler = async (data) => {
  try {
    await formDataApi.downloadWord(
      formName.value,
      {
        dataId: data._id
      },
      `${formOptions.formDef['description']}.docx`
    )
  } catch (error) {
    message.error(error.msg || '下载失败！')
  }
}
</script>
