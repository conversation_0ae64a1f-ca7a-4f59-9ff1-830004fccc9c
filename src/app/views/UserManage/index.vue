<template>
  <div class="form-design-max">
    <Suspense>
      <GTSearchTable
        :form-name="FORM_NAME"
        title="用户管理"
        @operate="tableOperatorClick"
        :tableOptions="tableOptions"
        ref="search_table"
        :customDicts="dicts"
      ></GTSearchTable>
    </Suspense>
    <a-modal
      class="form-design-model"
      getContainer=".form-design-max"
      destroyOnClose
      v-model:open="open"
      :title="`${title}用户`"
      :maskClosable="false"
      @ok="handleOk"
    >
      <FormModal
        :options="formOptions"
        @refreshTable="refreshTable"
        :formData="formData"
        :type="title"
        ref="GTForm"
        :roleMap="roleMap"
      />
    </a-modal>
    <a-modal v-model:open="openPassword" title="重置密码" @ok="handleOkPassword" width="500px">
      <a-form
        ref="passwordFormRef"
        :model="passwordForm"
        name="basic"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        autocomplete="off"
      >
        <a-form-item required label="输入密码" name="password" :rules="passwordRules">
          <a-input-password v-model:value="passwordForm.password" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'UserManage' })
import {
  searchItems,
  recordOperators,
  tableOperators,
  tableFields,
  dicts,
  formOptions,
  formDicts,
  getRoles
} from './config'
import { cloneDeep, keyBy } from 'lodash'
import FormModal from './components/Form/index.vue'
import { userApi } from '@gt/mis-components-web/apis'
import md5 from 'crypto-js/md5'
import { message } from 'ant-design-vue'
import { GTSearchTable } from '@gt/mis-components-web'
interface Props {
  passwordRegex: any
}
const props = withDefaults(defineProps<Props>(), {
  // eslint-disable-next-line vue/require-valid-default-prop
  passwordRegex: undefined
  // passwordRegex: {
  //   pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&+=.])(?=.*[^\w\d\s])\S{8,}$/,
  //   message: '必须包含大写字母、小写字母、数字和特殊字符，不少于8位'
  // }
})
//start table操作
const FORM_NAME = 'tb_user_info_map'
const search_table = ref()
const tableOptions = {
  tableFields,
  searchItems,
  tableOperators,
  recordOperators,
  dicts
}
async function refreshTable() {
  search_table.value.getList()
  open.value = false
}

const openPassword: Ref<boolean> = ref(false)
let changePasswordId = ''
const passwordRules: any = []
const passwordFormRef: Ref = ref()
const passwordForm = reactive({
  password: ''
})

if (props.passwordRegex) {
  passwordRules.push({
    trigger: 'blur',
    ...props.passwordRegex
  })
  tableOptions.recordOperators.map((item) => {
    if (item.key === '_reset') delete item.confirm
    return item
  })
}
formOptions.formDef.validators!.fieldValidators!.push({
  field: 'password',
  label: '密码',
  validators: [
    {
      type: 'regex',
      options: props.passwordRegex
        ? { ...props.passwordRegex }
        : {
            pattern: /^[a-zA-Z0-9]{4,16}$/,
            message: '仅可包含英文和数字,4-16位'
          }
    }
  ]
})
const handleOkPassword = async () => {
  try {
    await passwordFormRef.value.validate()
    const data = {
      password: md5(passwordForm.password).toString(),
      userIds: [changePasswordId]
    }
    let res = await userApi.resetUserPasswords(data)
    message.success(res)
  } catch (error) {
    console.log('error :>> ', error)
  }
}
//start GTform操作
const open: Ref<boolean> = ref(false)
let formData: Ref<object> = ref({})
const title: Ref<string> = ref('')
const GTForm = ref()
function tableOperatorClick({ key, data }) {
  switch (key) {
    case '_add':
      title.value = '添加'
      addHandler()
      break
    case '_reset':
      if (!props.passwordRegex) {
        resetHandler(data)
      } else {
        openPassword.value = true
        changePasswordId = data.user_id
      }
      break
    case '_edit':
      title.value = '编辑'
      editHandler(data)
      break
    case '_delete':
      deleteHandler(data.user_id)
      break
    case '_stop':
      enabledHandler(data.enabled, data.user_id)
      break
    case '_start':
      enabledHandler(data.enabled, data.user_id)
      break
  }
}
function addHandler() {
  open.value = true
}
async function resetHandler({ user_id }) {
  try {
    // data.userIds = [data.user_id]
    const params = {
      userIds: [user_id]
    }
    let res = await userApi.resetUserPasswords(params)
    message.success(res)
  } catch ({ msg }) {
    message.error('重置密码失败')
  }
}
function editHandler(data) {
  formData.value = data
  open.value = true
}
async function deleteHandler(id) {
  try {
    let res = await userApi.deleteUser(id)
    if (res._id) {
      message.success('删除成功')
      search_table.value.getList()
    }
  } catch (msg) {
    message.error('删除失败')
  }
}
async function enabledHandler(enabled, id) {
  try {
    let res
    if (enabled) {
      res = await userApi.suspendUser(id)
    } else {
      res = await userApi.resumeUser(id)
    }
    message.success(res)
    search_table.value.getList()
  } catch (msg) {
    message.error('停用失败')
  }
}
async function handleOk() {
  await GTForm.value.onSubmit()
}
//end GTform操作

//start GTform配置组合
const roleMap = ref()
async function setRoleDict() {
  const arr = await getRoles()
  roleMap.value = keyBy(arr, 'code')
  const cloneDict: FORM.DICT[] = cloneDeep(formDicts)
  const roleDict: FORM.DICT = {
    name: 'role',
    type: 'inline',
    items: arr
  }
  cloneDict.push(roleDict)
  formOptions.formDef.dicts = cloneDict
}
//end GTform配置组合
onMounted(async () => {
  await setRoleDict()
})
</script>

<style lang="less" scoped>
.form-design-max {
  width: 100%;
  height: 100%;
  :deep(.form-design-model) {
    width: 600px !important;
    top: 20px;
    padding-bottom: 0;
    height: calc(100% - 40px);
    > div {
      height: 100%;
      > .ant-modal-content {
        overflow-x: hidden;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        max-height: calc(100vh - 40px);
        padding-bottom: 0;
        > .ant-modal-footer {
          flex-shrink: 0;
          padding: 20px 5px;
        }
        > .ant-modal-body {
          height: 100%;
          overflow-y: auto;
          overflow-x: hidden;
        }
      }
    }
  }
}
</style>
