export {}
declare global {
  type EnumToUnion<T extends string | number> = T extends string ? `${T}` : T
  interface Window {
    import_meta: {
      VITE_APP_TITLE: string
      VITE_APP_VERSION: string
      VITE_APP_URL: string
      VITE_APP_AXIOS_BASE_URL: string
      VITE_APP_LON_LAT_SERVE?: string
      VITE_APP_PHOTO: string
      VITE_APP_FOOTER: string
      iconfontConfig?: {
        css_prefix_text: string
        [key: string]: any
      }
    }
  }
  type ThemeType = 'light' | 'dark'
}
