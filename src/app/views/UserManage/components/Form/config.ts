import { adminLayout, baseLayout } from '../../config'
import { cloneDeep } from 'lodash'
export function resultLayout(type) {
  const _adminLayout = cloneDeep(adminLayout)
  const _baseLayout = cloneDeep(baseLayout)
  let arr: any[] = []
  switch (type) {
    case '添加':
      arr = [..._adminLayout, ..._baseLayout]
      break
    case '编辑':
      arr = [_adminLayout[0], ..._baseLayout]
  }
  return arr
}
