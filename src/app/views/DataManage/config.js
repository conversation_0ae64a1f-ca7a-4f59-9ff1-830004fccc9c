export const searchItems = [
  // {
  //   operator: '=',
  //   renderer: {
  //     type: 'SelectField',
  //     options: {
  //       type: 'text',
  //       field: 'project_name',
  //       label: '项目区名称'
  //     }
  //   }
  // },
  {
    operator: '=',
    renderer: {
      type: 'SelectField',
      options: {
        type: 'text',
        field: 'zdfs',
        label: '整地方式',
        dict: {
          name: '整地方式',
          type: 'enum',
          items: ['深松+旋耕', '深翻+旋耕', '旋耕']
        }
      }
    }
  }
]
export const tableOperators = ['download']
export const recordOperators = [
  {
    key: '_view',
    label: '查看'
  },
  {
    key: '_download',
    label: '下载记录表'
  }
]

// 表单差异配置
// export const formConfig = {
//   soil_preparation: {
//     tableField: baseTableFields,
//     searchItems: baseSearchItems
//   }
// }
