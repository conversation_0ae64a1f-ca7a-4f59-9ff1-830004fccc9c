<template>
  <Suspense>
    <GTSearchTable
      v-if="tableOptions"
      ref="GTSearchTableRef"
      :tableOptions="tableOptions"
      @operate="recordOperatorHandleClick"
      formName="project_info"
      :showTitle="false"
      :yOffset="yOffset"
    />
  </Suspense>

  <a-modal v-model:open="manageVisible" width="80vw" title="项目管理">
    <a-tabs v-model:activeKey="manageActiveKey" type="card">
      <a-tab-pane :key="0" tab="实施地图">
        <div style="width: 100%; height: 75vh">
          <MapView></MapView>
        </div>
      </a-tab-pane>
      <a-tab-pane :key="1" tab="地块列表"> </a-tab-pane>
    </a-tabs>
  </a-modal>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ProjectInfoManage' })

import { GTSearchTable } from '@gt/mis-components-web'
import { searchItems, tableOperators, recordOperators } from './config'
import MapView from '@/app/components/MapView/index.vue'

const GTSearchTableRef = ref()
const manageVisible = ref(false)
const manageActiveKey = ref(0)
const yOffset = computed(() => {
  return 75 + 20 + 48 + 40 + 50 + 42 + 52
})

const tableOptions = computed(() => {
  const obj: any = {}
  obj.searchItems = searchItems
  obj.tableOperators = tableOperators
  obj.recordOperators = recordOperators
  obj.modalWidth = 600

  return obj
})

const recordOperatorHandleClick = ({ key, data }) => {
  switch (key) {
    case 'manage':
      manageVisible.value = true
      break
  }
}
</script>
