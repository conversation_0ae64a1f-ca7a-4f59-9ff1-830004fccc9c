import '@/assets/styles/base.less'
import '@/assets/styles/tailwind.css'
import 'core-js'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import stores from '@/stores'
import Antd from 'ant-design-vue'
import './common/work'
import packageJson from '../package.json' assert { type: 'json' }
import lowCodeComponents from '@gt/mis-components-web/lowCode'
console.log(
  `MIS当前版本 :>> ${packageJson.dependencies['@gt/mis-components-web'].replace('^', '')}`
)
/**  引入自定义打包依赖包 start */
// 解决日期选择器一半中文一半英文问题
import 'dayjs/locale/zh-cn'
import misComponents from '@gt/mis-components-web'
import '@gt/mis-components-web/style.css'
window.import_meta = import.meta.env
// 自定义iconfont值
import '@/assets/iconfont/iconfont.js'
import iconfontConfig from '@/assets/iconfont/iconfont.json'
window.import_meta.iconfontConfig = iconfontConfig
import { service } from '@gt/mis-components-web/apis'
service.defaults.baseURL = import.meta.env.VITE_APP_AXIOS_BASE_URL
// 去除路由warn
if (import.meta.env.DEV) {
  const originalWarn = console.warn
  console.warn = (msg) => {
    if (!msg.includes('[Vue Router warn]')) {
      originalWarn(msg)
    }
  }
}
/**  引入自定义打包依赖包 end */
import appComponents from '@/app/components'
const app = createApp(App)
// 去除warn提醒
app.config.warnHandler = function () {}
// app.use(createPinia())
// 全局挂载pinia
app.use(stores)
app.use(router)
app.use(Antd)
app.use(misComponents)
app.use(appComponents)
app.use(lowCodeComponents)
app.mount('#app')
