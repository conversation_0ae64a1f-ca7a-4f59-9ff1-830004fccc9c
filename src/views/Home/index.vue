<template>
  <div class="home-max">
    <NavBar class="navbar-box" />
    <div class="content-max">
      <MenuBar ref="MenuBarRef" />
      <div class="content-box">
        <RouterView :key="route.fullPath" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'Home' })
import { getMenuData, addRoutes, getFirstVisibleMenu } from './hooks/useRoute'
import { loginApi, formDataApi } from '@gt/mis-components-web/apis'
import NavBar from './layout/NavBar.vue'
import MenuBar from './layout/MenuBar.vue'
import { useInfoStore } from '@/stores'
const router = useRouter()
const route = useRoute()
const { setValue: setInfoStore } = useInfoStore()
const MenuBarRef = ref()
onMounted(async () => {
  await getUserInfo()
  const menuData = await getMenuData()
  if (menuData) {
    const { menuList, routes } = menuData
    setInfoStore('menuList', menuList)
    // console.log('routes, router :>> ', routes, router)
    addRoutes(routes, router)
    openRoute(menuList)
  }
})
// 获取用户信息并验证是否已登录
const getUserInfo = async () => {
  // 检查是否是登录状态
  const user_id = localStorage.getItem('user_id')
  if (!user_id) {
    router.push('/login')
    return
  }
  let data = await loginApi.getUserInformation()
  if (data.resetPassword === true) {
    router.replace('/ResetPassword')
  }
  // console.log('data :>> ', data)
  // resetPassword
  if (user_id) {
    try {
      const { list } = await formDataApi.searchFormData('tb_user_info_map', {
        filter: ['=', 'user_id', user_id]
      })
      if (list.length > 0) {
        data = Object.assign({}, list[0], data)
      }
    } catch (error) {
      console.log('用户信息映射表-error :>> ', error)
    }
  }
  setInfoStore('userInfo', data)
  localStorage.setItem('userInfo', JSON.stringify(data))
}
// 打开对应的页面
const openRoute = (menuList) => {
  if (route.path === '/') {
    const menu = getFirstVisibleMenu(menuList)
    MenuBarRef.value.menuClick(menu)
  } else {
    let { path, query } = route
    let baseKey = path
    if (Object.keys(query).length != 0) {
      baseKey += '?'
      for (const key in query) {
        baseKey += `${key}=${query[key]}&`
      }
      baseKey = baseKey.slice(0, baseKey.length - 1)
    }
    // console.log('baseKey :>> ', baseKey)
    MenuBarRef.value.menuClick({ key: baseKey })
  }
}
</script>

<style lang="less" scoped>
@navbar-height: 80px;
.home-max {
  width: 100%;
  height: 100%;
  .navbar-box {
    height: @navbar-height;
    line-height: @navbar-height;
  }
  .content-max {
    height: calc(100% - @navbar-height);
    display: flex;
    background: #f5f5f5;
    .content-box {
      flex: 1;
      overflow: hidden;
      height: calc(100% - 20px);
      background: #fff;
      margin: 10px;
    }
  }
}
</style>
