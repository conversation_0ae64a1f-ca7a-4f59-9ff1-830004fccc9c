<template>
  <div class="page-content">
    <div id="map">
      <GTSearch
        class="gtSearch"
        v-if="searchItems"
        :fields="searchItems"
        :defaultFields="defaultFields"
        :showNum="2"
        :listWidth="340"
        @search="searchHandler"
      />
      <legend-widget :layers="layers || []" @change="changelayerData"></legend-widget>
      <draw-select-widget @change="drawChange" @redo="redoCoordinates"></draw-select-widget>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as mapboxgl from 'mapbox-gl'
import 'mapbox-gl/dist/mapbox-gl.css'
import LegendWidget from './components/LegendWidget/index.vue'
import DrawSelectWidget from './components/DrawSelectWidget/index.vue'
import { MAP_BASE_CONFIG, pointOrange, pointBlue, pointGreen } from './config'

import { pointsWithinPolygon } from '@turf/turf'
import { gcj02towgs84 } from './utils/proj'
import DrawTool from './utils/mapDraw'

// 类型定义
interface MapConfig {
  layers: LayerConfig[]
  sources: SourceConfig[]
  point: PointConfig[]
}

interface LayerConfig {
  key: string | string[]
  open: boolean
  [key: string]: any
}

interface SourceConfig {
  id: string
  name: string
}

interface PointConfig {
  point: string
  key: string
}

interface SearchItem {
  [key: string]: any
}

interface DefaultFields {
  [key: string]: any
}

interface GeojsonData {
  [key: string]: any
}

interface SelectPoint {
  _id: string
  [key: string]: any
}

interface UserInfo {
  [key: string]: any
}

// Props 定义
interface Props {
  /** 地图的相关配置 */
  mapConfig: MapConfig
  /** 搜索项 */
  searchItems?: SearchItem[]
  /** 搜索项的默认值 */
  defaultFields?: DefaultFields
  /** 每种标记点的json数据，用于框选 */
  geojsonData?: GeojsonData
}

const props = withDefaults(defineProps<Props>(), {
  searchItems: () => [],
  defaultFields: () => ({}),
  geojsonData: () => ({})
})

// Emits 定义
interface Emits {
  (
    e: 'boundsInfo',
    boundsInfo: {
      x_max: number
      y_max: number
      x_min: number
      y_min: number
    }
  ): void
  (e: 'getSelectPoints', selectPoints: SelectPoint[]): void
  (e: 'search', values: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const map = ref<mapboxgl.Map | null>(null)
const layers = ref<LayerConfig[] | null>(null)
const selectPoints = ref<SelectPoint[]>([]) // 选择的点id
const fcode = ref<any>(null)
const baseZoomLevel = ref<number>(15.5)
const drawState = ref<boolean>(false) // 绘制状态
const zoomLevel = ref<number | null>(null) // zoom等级
const pointLoadStatus = ref<any>(null) // 地图核查点加载情况
const draw = ref<DrawTool | null>(null)
const userInfo = ref<UserInfo | null>(null)
const pointSourceGeoJson = ref<any>(null)

// 生命周期
onMounted(async () => {
  userInfo.value = JSON.parse(localStorage.getItem('userInfo') || '{}')
  await initMap()
})

// 方法定义
/** 初始化地图 */
const initMap = (): void => {
  ;(mapboxgl as any).accessToken =
    'pk.eyJ1IjoidmFuamsiLCJhIjoiY2tuemR0MDlrMDI5YzJ2bGNuaThwNjg3ZiJ9.Ix5XwtD2nE4rpqZ6u1j55Q'
  map.value = new mapboxgl.Map({
    ...MAP_BASE_CONFIG,
    ...props.mapConfig
  } as unknown as mapboxgl.MapboxOptions)
  map.value.on('load', mapLoaded)
}

/** 地图加载完成回调 */
const mapLoaded = (): void => {
  addImages()
  initDraw()
  addPointListener()
  setSource()

  if (!map.value) return

  map.value.on('moveend', () => {
    if (!map.value || map.value.getZoom() < baseZoomLevel.value) return
    const bounds = map.value.getBounds()

    const max = gcj02towgs84(bounds._ne.lng, bounds._ne.lat)
    const min = gcj02towgs84(bounds._sw.lng, bounds._sw.lat)
    const boundsInfo = {
      x_max: Number(max[0]),
      y_max: Number(max[1]),
      x_min: Number(min[0]),
      y_min: Number(min[1])
    }
    emit('boundsInfo', boundsInfo)
  })

  map.value.on('zoom', () => {
    if (map.value) {
      zoomLevel.value = map.value.getZoom()
    }
  })
  ;(window as any).map = map.value
  // 禁止双指旋转地图
  map.value.touchZoomRotate.disableRotation()
}
/** 删除所有sources和layer */
const removeSources = (): void => {
  if (!map.value || !props.mapConfig) return

  const { sources } = props.mapConfig
  sources.forEach((source) => {
    if (map.value!.getLayer(source.id)) map.value!.removeLayer(source.id)
    if (map.value!.getSource(source.id)) map.value!.removeSource(source.id)
  })
}

/** 设置数据源 */
const setSource = (): void => {
  if (!map.value || !props.mapConfig) return

  removeSources()
  const { sources } = props.mapConfig
  if (!sources || sources.length === 0) return
  sources.forEach((source) => {
    if (!map.value!.getSource(source.id)) {
      map.value!.addSource(source.id, {
        type: 'raster',
        tiles: [`${import.meta.env.VUE_APP_MAP_URL}api/maptiles/${source.name}/{z}/{x}/{y}.png`],
        maxzoom: 16
      })
      map.value!.addLayer({
        id: source.id,
        type: 'raster',
        source: source.id
      })
    }
  })
  layers.value = [...props.mapConfig.layers]

  props.mapConfig.layers.forEach((item) => {
    changelayerData(item)
  })
}
/** 添加图片 */
const addImages = (): void => {
  if (!map.value) return

  const imagesList = [
    {
      key: 'green',
      state: 'green',
      iconPath: pointGreen
    },
    {
      key: 'blue',
      state: 'blue',
      iconPath: pointBlue
    },
    {
      key: 'orange',
      state: 'orange',
      iconPath: pointOrange
    }
  ]

  for (const item of imagesList) {
    map.value.loadImage(item.iconPath, (error: any, image: any) => {
      if (error) throw error
      if (map.value) {
        map.value.addImage(item.key, image, {})
      }
    })
  }
}

/** 初始化绘制模块 */
const initDraw = (): void => {
  if (!map.value) return

  draw.value = new DrawTool(map.value, {
    showMessage: showMessage
  })

  map.value.on('click', (e) => {
    if (drawState.value && draw.value) {
      draw.value.addCoordinates([e.lngLat.lng, e.lngLat.lat])
    }
  })
}
/** 添加点击监听器 */
const addPointListener = (): void => {
  if (!map.value || !props.mapConfig) return
  const { point } = props.mapConfig
  if (!point || point.length === 0) return
  const point_list = point
  let lastEventState: any = null

  point_list.forEach(({ point }) => {
    map.value!.on('click', point, async (e: any) => {
      if (lastEventState && Date.now() - lastEventState.timestamp < 200) return
      // 保存这次点击的状态
      lastEventState = {
        timestamp: Date.now(),
        point: e.point
      }
      const features = map.value!.queryRenderedFeatures(e.point)
      await pointClick(features[0].properties)
    })
  })
}

/** 点击点处理 */
const pointClick = async (data: any): Promise<void> => {
  console.log('data', data)
  if (drawState.value) return

  const idToAdd = data._id
  const isFind = selectPoints.value.find((item) => item._id === idToAdd)

  if (isFind) {
    selectPoints.value = selectPoints.value.filter((item) => item._id !== idToAdd)
    await handlePointStyle()
    return
  }

  // 存放选中的数据
  selectPoints.value.push(data)
  await handlePointStyle()
}
/** 过滤图层 */
const changelayerData = (value: LayerConfig): void => {
  if (!map.value || !value) return

  if (Array.isArray(value.key)) {
    value.key.forEach((key) => {
      if (!map.value!.getLayer(key)) return
      map.value!.setLayoutProperty(key, 'visibility', value.open ? 'visible' : 'none')
    })
  } else {
    if (!map.value!.getLayer(value.key)) return
    map.value!.setLayoutProperty(value.key, 'visibility', value.open ? 'visible' : 'none')
  }
}

/** 绘制状态改变处理 */
const drawChange = (val: boolean): void => {
  if (!map.value) return

  drawState.value = val
  if (val) {
    draw.value?.activate('polygon')
  } else {
    endDraw()
    draw.value?.deactivate()
  }
}
/** 添加坐标 */
const addCoordinates = (e: any): void => {
  if (!e || !map.value) return
  const coord = map.value.getCenter()
  draw.value?.addCoordinates([coord.lng, coord.lat])
}

/** 撤销坐标 */
const redoCoordinates = (): void => {
  draw.value?.redoCoordinates()
}

/** 结束绘制 */
const endDraw = (): void => {
  if (!map.value) return

  const ary: any[] = []
  if (map.value.getZoom() > baseZoomLevel.value) {
    if (!draw.value) return
    const val = draw.value.getGeomData()
    const empty_obj = { features: [] }
    const point_list = props.mapConfig.point || []

    point_list.forEach((item) => {
      const ptsWithin =
        map.value!.getLayoutProperty(item.point, 'visibility') === 'none'
          ? empty_obj
          : pointsWithinPolygon(props.geojsonData[item.key], val)
      console.log('ptsWithin', ptsWithin)

      ptsWithin.features.forEach((items: any) => ary.push(items.properties))
    })
  }

  drawSelectPoint(ary)
}
/** 框选结果处理 */
const drawSelectPoint = async (list: any[]): Promise<void> => {
  const mapInstance = new Map()
  ;[...list, ...selectPoints.value].forEach((item) => {
    if (item._id) mapInstance.set(item._id, item)
  })

  selectPoints.value = Array.from(mapInstance.values()).filter(
    (item) => Object.keys(item).length > 0
  )

  emit('getSelectPoints', selectPoints.value)

  await handlePointStyle()
}

/** 点位选中的样式处理 */
const handlePointStyle = async (): Promise<void> => {
  if (!map.value) return

  if (selectPoints.value.length > 0) {
    const selectedPoints = selectPoints.value.map((item) => item._id)
    const value = ['match', ['get', '_id'], [...selectedPoints], 0.15, 0.1]
    const point_list = props.mapConfig.point || []

    point_list.forEach(({ point }) => {
      map.value!.setLayoutProperty(point, 'icon-size', value)
    })
  } else {
    const value = 0.1
    const point_list = props.mapConfig.point || []

    point_list.forEach(({ point }) => {
      map.value!.setLayoutProperty(point, 'icon-size', value)
    })
  }

  emit('getSelectPoints', selectPoints.value)
}
/** 添加点到图层 */
const addPointLayer = (geojson: any, id: string, iconName: string, size: number): void => {
  pointSourceGeoJson.value = geojson

  nextTick(() => {
    if (!map.value) return

    if (map.value.getSource(id)) {
      ;(map.value.getSource(id) as any).setData(geojson)
    } else {
      map.value.addSource(id, {
        type: 'geojson',
        data: geojson
      })
      map.value.addLayer({
        id,
        type: 'symbol',
        source: id,
        minzoom: baseZoomLevel.value,
        layout: {
          'icon-size': size,
          'icon-allow-overlap': true,
          'icon-image': iconName,
          visibility: 'visible'
        }
      })
    }
  })
}

/** 搜索处理器 */
const searchHandler = (values: any): void => {
  emit('search', values)
}

/** 重置点 */
const resetPoint = (): void => {
  selectPoints.value = []
  handlePointStyle()
}

/** 显示消息 */
const showMessage = (msg: any): void => {
  console.log(msg)
}

// 暴露给模板使用的方法
defineExpose({
  resetPoint,
  addPointLayer
})
</script>
<style scoped lang="less">
.page-content {
  width: 100%;
  height: 100%;
}
#map {
  position: relative;
  width: 100%;
  height: 100%;
}

.gtSearch {
  position: absolute;
  left: 0px;
  top: 0px;
  z-index: 8;
  background-color: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
  padding: 20px 20px 10px;
  margin: 0;
  // border-radius: 10px;
  width: 100%;
}

.bottom-container {
  background-color: #fff;
  position: absolute;
  padding: 15px;
  width: 400px;
  left: 0;
  right: 0;
  margin: 0 auto;
  bottom: 10px;
  z-index: 999;
  border-radius: 10px;
}

.modal {
  /deep/.ant-modal {
    top: 50px;
    overflow: hidden;
    .ant-modal-content {
      max-height: calc(100vh - 100px);
      display: flex;
      flex-direction: column;
      .ant-modal-header,
      .ant-modal-footer {
        flex-shrink: 0;
      }
      .ant-modal-body {
        padding: 20px;
        flex: 1;

        overflow-y: auto;
      }
    }
  }
}
</style>
